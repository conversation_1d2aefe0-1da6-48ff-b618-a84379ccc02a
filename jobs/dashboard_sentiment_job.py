from typing import Optional, List
from pydantic import BaseModel
import asyncio
import argparse
import json
import re
import datetime

from jobs.base_job import Base<PERSON>ob, JOB_CONFIG_PATH
from lib.common.logger import logger
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig, Config
from lib.llm_service import LLMService, LLMServiceError

RESPONSE_SENTIMENT_MAP = {"positive": 1, "negative": 2, "neutral": 3, "unrelated": 4}
COMMENT_SENTIMENT_MAP = {1: "positive", 2: "negative", 3: "neutral", 4: "unrelated"}


class EnrichConfig(BaseModel):
    days_lookback: int
    source_table: str
    post_source_table: Optional[str] = None
    target_table: str
    content_type: str
    channel: str
    sub_channel: Optional[str] = None
    channel_type: str
    author_type: Optional[str] = None

    @classmethod
    def model_validate(cls, data: dict):
        if data["content_type"] == "comment" and data["post_source_table"] is None:
            raise ValueError("post_source_table must be specified for comment")
        return super().model_validate(data)


class DashboardMetadata(BaseModel):
    source_table: str
    post_id: Optional[str] = None
    post_content: Optional[str] = None


class DashboardReportData(BaseModel):
    id: str
    timestamp: datetime.datetime
    content: str
    channel: str
    sub_channel: Optional[str] = None
    channel_type: str
    content_type: str
    sentiment_type_id: int
    topics: List[str]
    # topic: str
    author_type: str
    metadata: str

    @classmethod
    def model_validate(cls, data: dict, job_config: EnrichConfig):
        metadata = DashboardMetadata.model_validate(job_config.model_dump())
        data["metadata"] = json.dumps(metadata.model_dump())
        # change field name
        data["topics"] = data["topic"]
        # data.update(sentiment_response)
        # override author_type
        if job_config.author_type:
            data["author_type"] = job_config.author_type

        data["sentiment_type_id"] = RESPONSE_SENTIMENT_MAP[data["sentiment"]]
        data.update(
            {
                "channel": job_config.channel,
                "sub_channel": job_config.sub_channel,
                "channel_type": job_config.channel_type,
                "content_type": job_config.content_type,
            }
        )
        return super().model_validate(data)


class SentimentJob(BaseJob):

    POST_ENDPOINT = "/api/sentiment/post"
    COMMENT_ENDPOINT = "/api/sentiment/comment"

    def __init__(
        self,
        channel_name,
        job_config_path: str = JOB_CONFIG_PATH,
        days_lookback: Optional[int] = None,
        db_config: DBConfig = SecretConfig().get_db_config(),
        llm_service_url: str = SecretConfig().get_llm_service_url(),
    ):
        self.channel_name = channel_name
        self.db = Database(db_config)
        self.llm_service = LLMService(llm_service_url)
        self.job_config = self._get_config(channel_name, job_config_path)

        # if days lookback is set, override
        self.job_config.days_lookback = self._get_days_lookback(days_lookback)

    def _get_config(self, channel_name, job_config_path: str) -> EnrichConfig:
        config = Config(job_config_path)
        job_config = config.get_config([channel_name, "enrich_config"])
        if not isinstance(job_config, dict):
            raise ValueError("enrich_config must be a dictionary")
        return EnrichConfig.model_validate(job_config)

    def _get_days_lookback(self, days_lookback: Optional[int]) -> int:
        if days_lookback:
            return days_lookback
        else:
            return self.job_config.days_lookback

    def _get_post_data(self) -> list[dict]:
        sql = f"""
            SELECT id, timestamp,content
            FROM {self.job_config.source_table}
            WHERE (timestamp >= (now() - interval '{self.job_config.days_lookback} days')) and content IS NOT NULL
        """
        results = self.db.execute(query=sql)
        logger.info(f"Found {len(results)} results from post source table")
        return results

    def _get_comment_data(self) -> list[dict]:
        # join comment table with post table
        if self.channel_name == "pantip-comment-mention":
            join_column = "mention_id"
        else:
            join_column = "post_id"
        # sql = f"""
        #     SELECT c.id as id, c.timestamp as timestamp, c.content as content, p.content as post
        #     FROM {self.job_config.source_table} c
        #     JOIN {self.job_config.post_source_table} p ON c.{join_column} = p.id
        #     WHERE (c.timestamp >= (now() - interval '{self.job_config.days_lookback} days')) and c.content IS NOT NULL
        # """

        sql = f"""
            SELECT c.id as id, c.timestamp as timestamp, c.content as content, p.content as post, p.id as post_id, p.topic as post_topic, p.sentiment_type_id as post_sentiment
            FROM {self.job_config.source_table} c
            JOIN {self.job_config.target_table} p ON c.{join_column} = p.id
            WHERE (c.timestamp >= (now() - interval '{self.job_config.days_lookback} days')) and c.content IS NOT NULL and p.topic != 'Unrelated'
        """
        results = self.db.execute(query=sql)

        for item in results:
            # convert post sentiment to response sentiment
            item["post_sentiment"] = COMMENT_SENTIMENT_MAP[item["post_sentiment"]]

        logger.info(f"Found {len(results)} results from comment source table")
        return results

    def _get_data(self) -> list[dict]:
        # get post data
        if self.job_config.content_type == "post":
            return self._get_post_data()
        else:
            return self._get_comment_data()

    def _is_enriched(self, data: dict) -> bool:
        sql = f"""
            SELECT *
            FROM {self.job_config.target_table}
            WHERE id = '{data['id']}'
        """
        results = self.db.execute(query=sql)
        return len(results) > 0
    
    def

    def _format_post_payload(self, data: dict) -> str:
        post_content = self._clean_text(data["content"])
        payload = {"post": post_content}
        json_payload = json.dumps(payload, ensure_ascii=False)
        return json_payload

    def _format_comment_payload(self, data: dict) -> str:
        comment_content = self._clean_text(data["content"])
        post_content = self._clean_text(data["post"])
        payload = {
            "post": post_content,
            "comment": comment_content,
        }
        json_payload = json.dumps(payload, ensure_ascii=False)
        return json_payload

    def _clean_text(self, text: str) -> str:
        text = self._remove_invisible_unicode(text)
        text = self._remove_hashtags(text)
        text = self._remove_links(text)
        text = self._remove_extra_spaces(text)
        return text

    def _remove_invisible_unicode(self, text: str) -> str:
        # remove invisible unicode but keep thai characters
        return re.sub(
            r"[\u200b\u200c\u200d\u200e\u200f\u202a-\u202e\u2060\uFEFF]",
            "",
            text,
        )

    def _remove_hashtags(self, text: str) -> str:
        # remove hashtag, except #medeze
        # Regex to find hashtags: words starting with #

        # We'll remove all except the one matching keep_tag (case-insensitive)
        keep_tag = "medeze"
        pattern = re.compile(r"#(\S+)", re.IGNORECASE)

        def repl(m):
            tag = m.group(1)
            if tag.lower() == keep_tag.lower():
                return m.group(0)  # keep the hashtag as is
            else:
                return ""  # remove other hashtags

        return pattern.sub(repl, text)

    def _remove_links(self, text: str) -> str:
        return re.sub(r"http\S+", "", text)

    def _remove_extra_spaces(self, text: str) -> str:
        return re.sub(r"\s+", " ", text).strip()

    def _save_to_target_table(self, data: list[dict]):
        self.db.upsert(
            table=self.job_config.target_table, data=data, unique_columns=["id"]
        )
        logger.info(f"Save {data} to target table")

    async def _get_sentiment(self, data: dict) -> dict | None:
        # format payload
        if self.job_config.content_type == "post":
            payload = self._format_post_payload(data)
            endpoint = self.POST_ENDPOINT
        else:
            payload = self._format_comment_payload(data)
            endpoint = self.COMMENT_ENDPOINT

        # call llm service
        try:
            response = await self.llm_service.post(endpoint=endpoint, data=payload)
            if response is None:
                return None
            # add result to original data
            response.update(data)
            return response
        except LLMServiceError as e:
            logger.error(f"LLM service error: {e} for {data['id']}")
            return None

    async def run(self):
        fail_count = 0
        # get data from source table
        data = self._get_data()

        new_data = self._filter_enriched_data(data)

        ## for loop
        for item in data:
            # check if already enrichment in target table
            if self._is_enriched(item):
                logger.info(f"Data with id {item['id']} already enriched")
            else:
                tasks = []
                for i, item in enumerate(data):
                    logger.info(f"Scheduling request for {item['id']}...")
                    # Schedule the coroutine but don't await yet
                    tasks.append(job._get_sentiment(item))

                # Await all tasks concurrently
                responses = await asyncio.gather(*tasks, return_exceptions=True)

                results = []
                for i, response in enumerate(responses):
                    if isinstance(response, Exception):
                        logger.error(f"Error on request {i}: {response}")
                        continue
                    if response is None:
                        continue
                    results.append(response)

                # count None in results
                fail_count = results.count(None)
                logger.info(f"None count: {fail_count}")

                # response = await self._get_sentiment(item)
                # if response is None:
                #     fail_count += 1
                #     continue

                result_data = [
                    DashboardReportData.model_validate(item, self.job_config)
                    for item in results
                ]
                # DashboardReportData.model_validate(
                #     item, self.job_config
                # )
                # logger.info(f"Result data: {result_data}")

                # save to target table
                self._save_to_target_table(
                    [result_data.model_dump(mode="json") for result_data in result_data]
                )

        logger.info(f"Failed to get sentiment analysis for {fail_count} items")

    async def test(self):
        # get data from source table
        # get data from source table
        with open("dev/dashboard-sentiment/all_posts.json", "r", encoding="utf-8") as f:
            data = json.load(f)
        tasks = []
        for i, item in enumerate(data):
            d = {"id": i, "content": item}
            logger.info(f"Scheduling request for {d['id']}...")
            # Schedule the coroutine but don't await yet
            tasks.append(job._get_sentiment(d))

        # Await all tasks concurrently
        responses = await asyncio.gather(*tasks, return_exceptions=True)

        results = []
        for i, response in enumerate(responses):
            if isinstance(response, Exception):
                logger.error(f"Error on request {i}: {response}")
                continue
            if response is None:
                continue
            results.append(response)

        # write to json
        with open(
            "dev/dashboard-sentiment/all_post_response.json", "w", encoding="utf-8"
        ) as f:
            json.dump(results, f, ensure_ascii=False, indent=4)


if __name__ == "__main__":
    # Set up argument parser
    parser = argparse.ArgumentParser(
        description="Run sentiment/topic job for a specific channel"
    )
    parser.add_argument(
        "channel_name",
        type=str,
        help="Name of the channel to process sentiment/topic (required)",
    )

    # Parse arguments
    args = parser.parse_args()

    logger.info("Starting the job...")

    job = SentimentJob(channel_name=args.channel_name, days_lookback=365)
    # results = job._get_data()
    # for item in results:
    #     # delete newline in post
    #     item["post"] = item["post"].replace("\n", " ")
    #     print(f"post: {item['post']}")
    #     print(f"comment: {item['content']}")
    #     print(f"post_sentiment: {item['post_sentiment']}")
    #     print(f"post_topic: {item['post_topic']}")
    #     print("")
    # asyncio.run(job.test())
    asyncio.run(job.run())

    logger.info("Job completed successfully.")
