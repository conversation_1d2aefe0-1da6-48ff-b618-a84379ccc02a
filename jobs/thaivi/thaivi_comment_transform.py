from lib.common.logger import logger
from jobs.base_job import TransformJob, JOB_CONFIG_PATH, RAW_DATA_PATH
from lib.storage.gcs import GCS
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig, Config
from pydantic import BaseModel, HttpUrl
import pendulum
import datetime


class ThaiViComment(BaseModel):
    id: str
    timestamp: datetime.datetime
    post_id: str
    comment_number: str
    author_name: str
    content: str

    @staticmethod
    def convert_timestamp(timestamp_str: str) -> pendulum.DateTime:
        # Convert Thai/English timestamp format to datetime
        thai_months = {
            "ม.ค.": "01",
            "ก.พ.": "02",
            "มี.ค.": "03",
            "เม.ย.": "04",
            "พ.ค.": "05",
            "มิ.ย.": "06",
            "ก.ค.": "07",
            "ส.ค.": "08",
            "ก.ย.": "09",
            "ต.ค.": "10",
            "พ.ย.": "11",
            "ธ.ค.": "12",
            "Jan": "01",
            "Feb": "02",
            "Mar": "03",
            "Apr": "04",
            "May": "05",
            "Jun": "06",
            "Jul": "07",
            "Aug": "08",
            "Sep": "09",
            "Oct": "10",
            "Nov": "11",
            "Dec": "12",
        }

        # Split the timestamp components
        parts = timestamp_str.replace(",", "").split()

        # Handle both formats: "<|im_start|>ทร์ ธ.ค. 30, 2024 2:36 pm" or "Sat Dec 21, 2024 11:01 am"
        if len(parts) == 6:
            # Thai format with day name
            _, month, day, year, time, meridiem = parts
        elif len(parts) == 5:
            # English format
            _, month, day, year, time_meridiem = parts
            # Split time and meridiem if they're combined
            if "am" in time_meridiem.lower() or "pm" in time_meridiem.lower():
                time = time_meridiem[:-2]
                meridiem = time_meridiem[-2:]
            else:
                time, meridiem = time_meridiem.split()
        else:
            raise ValueError(f"Unsupported timestamp format: {timestamp_str}")

        hour, minute = time.split(":")

        # Convert to 24-hour format
        hour = int(hour)
        if meridiem.lower() == "pm" and hour != 12:
            hour += 12
        elif meridiem.lower() == "am" and hour == 12:
            hour = 0

        # Create datetime string in ISO format with Bangkok timezone
        datetime_str = f"{year}-{thai_months[month]}-{day.zfill(2)} {str(hour).zfill(2)}:{minute}:00"
        # Parse datetime in Bangkok timezone and convert to UTC
        dt_bkk = pendulum.parse(datetime_str, tz="Asia/Bangkok")
        dt_utc = dt_bkk.in_timezone("UTC")  # type: ignore
        return dt_utc

    @classmethod
    def parse(cls, data: dict):
        data["timestamp"] = cls.convert_timestamp(data["timestamp"])
        return cls.model_validate(data)


class ThaiViCommentTransformJob(TransformJob):
    CHANNEL_NAME = "thaivi"

    def __init__(
        self,
        job_config_path: str = JOB_CONFIG_PATH,
        gcs_credentials_path: str = SecretConfig().get_datalake_bucket_credentials_path(),
        gcs_bucket: str = SecretConfig().get_datalake_bucket(),
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.gcs = GCS(credentials_path=gcs_credentials_path, bucket_name=gcs_bucket)
        self.db = Database(config=db_config)

        target_table = Config(job_config_path).get_config(
            [self.CHANNEL_NAME, "comment_target_table"]
        )
        if not isinstance(target_table, str):
            raise ValueError("Target table must be a string")
        self.target_table = target_table

    def extract(self):
        # Get the most recent ingest date
        ingest_date = pendulum.now().format("YYYY-MM-DD")
        gcs_path = f"raw/{self.CHANNEL_NAME}/ingest_date={ingest_date}/comments.json"

        # Download from GCS
        data = self.gcs.read_json_file(gcs_path)

        return data

    def transform(self, data):
        return [ThaiViComment.parse(item) for item in data]

    def load(self, data):
        # Convert pydantic models to dictionaries
        data_dicts = [item.model_dump(mode="json") for item in data]

        # Upsert data into database
        rows_affected = self.db.upsert(
            table=self.target_table, data=data_dicts, unique_columns=["id"]
        )
        logger.info(
            f"Transformed data saved to database table: {self.target_table} ({rows_affected} rows affected)"
        )

    def run(self):
        data = self.extract()
        transformed_data = self.transform(data)
        self.load(transformed_data)


if __name__ == "__main__":
    logger.info("Starting the job...")
    logger.info("Run thaivi comment transform job")
    job = ThaiViCommentTransformJob()
    job.run()
    logger.info("Job completed successfully.")
