from lib.scraper.pantip import (
    PantipScraper,
    PantipTopicMetadata,
    PantipPost,
    PantipComment,
    PantipMention,
)
from jobs.base_job import BaseJob, JOB_CONFIG_PATH
from lib.storage.database import Database, DBConfig
from lib.common.config import SecretConfig
from lib.common.config import Config

from typing import List
from lib.common.logger import logger
import json
from pydantic import BaseModel


class PantipSearchConfig(BaseModel):
    keyword: str
    pages_lookback: int


class PantipJob(BaseJob):
    METADATA_TABLE = "data_pantip_topic_metadata"
    POST_TABLE = "data_pantip_posts"
    MENTION_TABLE = "data_pantip_mentions"
    COMMENT_TABLE = "data_pantip_comments"

    def __init__(
        self,
        job_config_path: str = JOB_CONFIG_PATH,
        db_config: DBConfig = SecretConfig().get_db_config(),
    ):
        self.job_config = Config(job_config_path)

        self.scraper = PantipScraper()
        self.db = Database(db_config)

        self.post_count = 0
        self.mention_count = 0
        self.comment_count = 0

    def _save_posts(self, posts: List[PantipPost]) -> int:
        # convert pydantic model to dict
        posts_dict = [item.model_dump(mode="json") for item in posts]
        if posts_dict:
            self.db.upsert(self.POST_TABLE, posts_dict, ["id"])
            logger.info(f"Saved {len(posts_dict)} posts to database")
            return len(posts_dict)
        else:
            logger.info("No posts to save")
            return 0

    def _save_mentions(self, mentions: List[PantipMention]) -> int:
        # convert pydantic model to dict
        mentions_dict = [item.model_dump(mode="json") for item in mentions]
        if mentions_dict:
            self.db.upsert(self.MENTION_TABLE, mentions_dict, ["id"])
            logger.info(f"Saved {len(mentions_dict)} mentions to database")
            return len(mentions_dict)
        else:
            logger.info("No mentions to save")
            return 0

    def _save_comments(self, comments: List[PantipComment]) -> int:
        # convert pydantic model to dict
        comments_dict = [item.model_dump(mode="json") for item in comments]
        if comments_dict:
            self.db.upsert(self.COMMENT_TABLE, comments_dict, ["id"])
            logger.info(f"Saved {len(comments_dict)} comments to database")
            return len(comments_dict)
        else:
            logger.info("No comments to save")
            return 0

    def _fetch_post(self, url: str) -> tuple[PantipPost, List[PantipComment]] | None:
        result = self.scraper.scrape_post(url)
        if result is not None:
            post_data, comment_data = result
            return post_data, comment_data
        else:
            logger.warning(f"Failed to scrape post: {url}")
            return None

    def _fetch_mention(
        self, url: str, comment_no: str
    ) -> tuple[PantipMention, List[PantipComment]] | None:
        result = self.scraper.scrape_mention(url, comment_no)
        if result is not None:
            mention_data, comments_of_mention = result
            return mention_data, comments_of_mention
        else:
            logger.warning(f"Failed to scrape mention: {url}")
            return None

    def _process_post(self, url: str) -> tuple[PantipPost, List[PantipComment]] | None:
        logger.info(f"Scraping post: {url}")
        result = self._fetch_post(url)
        if result is not None:
            post_data, comment_data = result
            post_saved = self._save_posts([post_data])
            comment_saved = self._save_comments(comment_data)
            # update count
            self.post_count += post_saved
            self.comment_count += comment_saved
            return post_data, comment_data
        else:
            logger.warning(f"Failed to scrape post: {url}")
            return None

    def _process_mention(
        self, url: str, comment_no: str
    ) -> tuple[PantipMention, List[PantipComment]] | None:
        logger.info(f"Scraping mention: {url}")
        result = self._fetch_mention(url, comment_no)
        if result is not None:
            mention_data, comments_of_mention = result
            mention_saved = self._save_mentions([mention_data])
            comment_saved = self._save_comments(comments_of_mention)
            # update count
            self.mention_count += mention_saved
            self.comment_count += comment_saved
            return mention_data, comments_of_mention
        else:
            logger.warning(f"Failed to scrape mention: {url}")
            return None

    def run(self):
        pass
