import asyncio
import json
from typing import Dict, Any, Optional, cast
import aiohttp
from urllib.parse import urljoin
from tenacity import (
    retry,
    stop_after_attempt,
    wait_exponential,
    retry_if_exception_type,
    RetryError,
    before_sleep_log,
)

from lib.common.logger import logger


class LLMServiceError(Exception):
    """Exception raised for LLM service errors."""

    def __init__(self, status_code: int, message: str):
        self.status_code = status_code
        self.message = message
        super().__init__(f"LLM service error: {status_code} - {message}")


class LLMService:
    def __init__(
        self,
        base_url: str,
        api_key: Optional[str] = None,
        timeout: int = 30,
    ):
        """
        Initialize the LLMService with configuration parameters.

        Args:
            base_url (str): The base URL of the LLM service
            api_key (str, optional): API key for authentication
            timeout (int): Request timeout in seconds
            max_retries (int): Maximum number of retry attempts
            retry_delay (int): Initial delay between retries in seconds
        """
        self.base_url = base_url.rstrip("/")
        self.api_key = api_key
        self.timeout = timeout
        self.session = None

    async def __aenter__(self):
        """Set up the aiohttp session when used as a context manager."""
        if self.session is None:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout),
                headers=self._get_headers(),
            )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Close the aiohttp session when exiting context manager."""
        await self.close()

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for API requests."""
        headers = {"Content-Type": "application/json", "Accept": "application/json"}
        if self.api_key:
            headers["Authorization"] = f"Bearer {self.api_key}"
        return headers

    @retry(
        retry=retry_if_exception_type((aiohttp.ClientError, asyncio.TimeoutError)),
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=1, max=10),
        before_sleep=before_sleep_log(logger, log_level=20),  # INFO level
        reraise=True,
    )
    async def _make_request(
        self,
        method: str,
        endpoint: str,
        data: Optional[Dict[str, Any]] = None,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make an HTTP request to the LLM service with retry logic.

        Args:
            method (str): HTTP method (GET, POST, etc.)
            endpoint (str): API endpoint path
            data (dict, optional): Request body data
            params (dict, optional): URL query parameters

        Returns:
            dict: Response data

        Raises:
            LLMServiceError: If the request fails after retries
        """
        url = urljoin(self.base_url, endpoint)

        if self.session is None:
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.timeout),
                headers=self._get_headers(),
            )

        try:
            async with self.session.request(
                method=method, url=url, data=data, params=params
            ) as response:
                response.raise_for_status()
                return await response.json()

        except aiohttp.ClientResponseError as e:
            logger.error(f"Request failed with status {e.status}: {e.message}")
            # Only retry on specific status codes, otherwise raise immediately
            if e.status not in (408, 429, 500, 502, 503, 504):
                raise LLMServiceError(e.status, e.message)
            raise  # Let tenacity handle the retry for retryable status codes
        except (aiohttp.ClientError, asyncio.TimeoutError) as e:
            logger.error(f"Request failed: {str(e)}")
            raise  # Let tenacity handle the retry
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            raise LLMServiceError(500, str(e))

    async def get(
        self, endpoint: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a GET request to the LLM service.

        Args:
            endpoint (str): API endpoint path
            params (dict, optional): URL query parameters

        Returns:
            dict: Response data
        """
        try:
            return await self._make_request("GET", endpoint, params=params)
        except RetryError as e:
            # Handle case when all retries are exhausted
            if e.last_attempt.exception():
                logger.error(f"All retries failed: {str(e.last_attempt.exception())}")
                if isinstance(e.last_attempt.exception(), aiohttp.ClientResponseError):
                    err = cast(aiohttp.ClientResponseError, e.last_attempt.exception())
                    raise LLMServiceError(err.status, err.message)
            raise LLMServiceError(500, "Maximum retry attempts reached")

    async def post(
        self,
        endpoint: str,
        data: str,
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make a POST request to the LLM service.

        Args:
            endpoint (str): API endpoint path
            data (dict): Request body data
            params (dict, optional): URL query parameters

        Returns:
            dict: Response data
        """
        try:
            return await self._make_request("POST", endpoint, data=data, params=params)
        except RetryError as e:
            # Handle case when all retries are exhausted
            if e.last_attempt.exception():
                logger.error(f"All retries failed: {str(e.last_attempt.exception())}")
                if isinstance(e.last_attempt.exception(), aiohttp.ClientResponseError):
                    err = cast(aiohttp.ClientResponseError, e.last_attempt.exception())
                    raise LLMServiceError(err.status, err.message)
            raise LLMServiceError(500, "Maximum retry attempts reached")

    async def put(
        self,
        endpoint: str,
        data: Dict[str, Any],
        params: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Make a PUT request to the LLM service.

        Args:
            endpoint (str): API endpoint path
            data (dict): Request body data
            params (dict, optional): URL query parameters

        Returns:
            dict: Response data
        """
        try:
            return await self._make_request("PUT", endpoint, data=data, params=params)
        except RetryError as e:
            # Handle case when all retries are exhausted
            if e.last_attempt.exception():
                logger.error(f"All retries failed: {str(e.last_attempt.exception())}")
                if isinstance(e.last_attempt.exception(), aiohttp.ClientResponseError):
                    err = cast(aiohttp.ClientResponseError, e.last_attempt.exception())
                    raise LLMServiceError(err.status, err.message)
            raise LLMServiceError(500, "Maximum retry attempts reached")

    async def delete(
        self, endpoint: str, params: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Make a DELETE request to the LLM service.

        Args:
            endpoint (str): API endpoint path
            params (dict, optional): URL query parameters

        Returns:
            dict: Response data
        """
        try:
            return await self._make_request("DELETE", endpoint, params=params)
        except RetryError as e:
            # Handle case when all retries are exhausted
            if e.last_attempt.exception():
                logger.error(f"All retries failed: {str(e.last_attempt.exception())}")
                if isinstance(e.last_attempt.exception(), aiohttp.ClientResponseError):
                    err = cast(aiohttp.ClientResponseError, e.last_attempt.exception())
                    raise LLMServiceError(err.status, err.message)
            raise LLMServiceError(500, "Maximum retry attempts reached")

    async def close(self):
        """
        Close the session and release resources.
        """
        if self.session:
            await self.session.close()
            self.session = None
