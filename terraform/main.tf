terraform {
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

resource "google_workflows_workflow" "discord-notification" {
  name            = "discord-notification"
  description     = "Discord notification workflow"
  project         = var.project_id
  region          = var.region
  service_account = "medeze-data-jobs-sa@${var.project_id}.iam.gserviceaccount.com"
  source_contents = file("template/discord.yaml")
}
# Cloud Run Job for data processing
module "quant-price-performance" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-quant-price-performance" = {
      python_file_path = "jobs/quant_price_performance/main.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }

  }
  workflow_name        = "wf-quant-price-performance"
  workflow_description = "Jobs fetch data from set for quant price performance"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-facebook" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-facebook-post-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["facebook-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-post-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["facebook-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-comment-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["facebook-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-facebook-comment-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["facebook-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-facebook-post-ingest",
    "jobs-facebook-post-transform",
    "jobs-facebook-comment-ingest",
    "jobs-facebook-comment-transform"
  ]
  workflow_name        = "wf-apify-facebook"
  workflow_description = "Jobs scrape data from facebook with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-instagram" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-instagram-post-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["instagram-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-post-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["instagram-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-comment-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["instagram-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-instagram-comment-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["instagram-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-instagram-post-ingest",
    "jobs-instagram-post-transform",
    "jobs-instagram-comment-ingest",
    "jobs-instagram-comment-transform"
  ]

  workflow_name        = "wf-apify-instagram"
  workflow_description = "Jobs scrape data from instagram with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-tiktok" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-tiktok-post-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["tiktok-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-post-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["tiktok-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-comment-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["tiktok-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-tiktok-comment-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["tiktok-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }

  }
  job_order = [
    "jobs-tiktok-post-ingest",
    "jobs-tiktok-post-transform",
    "jobs-tiktok-comment-ingest",
    "jobs-tiktok-comment-transform"
  ]
  workflow_name        = "wf-apify-tiktok"
  workflow_description = "Jobs scrape data from tiktok with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-youtube" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-youtube-post-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["youtube-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-post-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["youtube-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-comment-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["youtube-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-youtube-comment-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["youtube-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }

  }
  job_order = [
    "jobs-youtube-post-ingest",
    "jobs-youtube-post-transform",
    "jobs-youtube-comment-ingest",
    "jobs-youtube-comment-transform"
  ]
  workflow_name        = "wf-apify-youtube"
  workflow_description = "Jobs scrape data from youtube with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "apify-twitter" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-twitter-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["twitter-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-post-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["twitter-post"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-comment-ingest" = {
      python_file_path = "jobs/apify/ingest.py"
      python_args      = ["twitter-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-twitter-comment-transform" = {
      python_file_path = "jobs/apify/transform.py"
      python_args      = ["twitter-comment"]
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-twitter-ingest",
    "jobs-twitter-post-transform",
    "jobs-twitter-comment-ingest",
    "jobs-twitter-comment-transform"
  ]
  workflow_name        = "wf-apify-twitter"
  workflow_description = "Jobs scrape data from twitter with apify"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "thaivi" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-thaivi-ingest" = {
      python_file_path = "jobs/thaivi/thaivi_ingest.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    },
    "jobs-thaivi-post-transform" = {
      python_file_path = "jobs/thaivi/thaivi_post_transform.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    },
    "jobs-thaivi-comment-transform" = {
      python_file_path = "jobs/thaivi/thaivi_comment_transform.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-thaivi-ingest",
    "jobs-thaivi-post-transform",
    "jobs-thaivi-comment-transform"
  ]
  workflow_name        = "wf-thaivi"
  workflow_description = "Jobs scrape data from Thaivi webboard"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "pantip" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-pantip-update" = {
      python_file_path = "jobs/pantip/update.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    },
    "jobs-pantip-search" = {
      python_file_path = "jobs/pantip/search.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "14400s"
      max_retries      = 3
    }
  }
  job_order = [
    "jobs-pantip-update",
    "jobs-pantip-search"
  ]
  workflow_name        = "wf-pantip"
  workflow_description = "Jobs scrape data from Pantip"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}

module "news" {
  source = "./modules"

  project_id      = var.project_id
  region          = var.region
  container_image = var.container_image
  jobs = {
    "jobs-news" = {
      python_file_path = "jobs/news.py"
      cpu              = "1"
      memory           = "512Mi"
      timeout          = "600s" # min
      max_retries      = 3
    }
  }

  workflow_name        = "wf-news"
  workflow_description = "Jobs scrape data from Google News"
  discord_workflow_id  = google_workflows_workflow.discord-notification.name
  schedule_bkk         = "0 7 * * *"
}
