[project]
name = "medeze-data-jobs"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.12.13",
    "apify-client>=1.9.3",
    "assertpy>=1.1",
    "black>=25.1.0",
    "bs4>=0.0.2",
    "google-cloud-storage>=3.1.0",
    "pendulum>=3.0.0",
    "playwright>=1.51.0",
    "pre-commit>=4.2.0",
    "psycopg2-binary>=2.9.10",
    "pydantic>=2.11.3",
    "pytest>=8.3.5",
    "trafilatura>=2.0.0",
    "tenacity>=9.1.2",
]

[tool.uv]
package = true

[tool.setuptools]
py-modules = ["lib","jobs"]

[tool.pytest.ini_options]
pythonpath = ["."]
